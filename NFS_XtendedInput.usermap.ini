[Events]
FRONTENDACTION_UP = XINPUT_GAMEPAD_DPAD_UP
FRONTENDACTION_DOWN = XINPUT_GAMEPAD_DPAD_DOWN
FRONTENDACTION_LEFT = XINPUT_GAMEPAD_DPAD_LEFT
FRONTENDACTION_RIGHT = XINPUT_GAMEPAD_DPAD_RIGHT
FRONTENDACTION_UP_ALT = XINPUT_GAMEPAD_LS_UP
FRONTENDACTION_DOWN_ALT = XINPUT_GAMEPAD_LS_DOWN
FRONTENDACTION_LEFT_ALT = XINPUT_GAMEPAD_LS_LEFT
FRONTENDACTION_RIGHT_ALT = XINPUT_GAMEPAD_LS_RIGHT
FRONTENDACTION_ACCEPT = XINPUT_GAMEPAD_A
FRONTENDACTION_CANCEL = XINPUT_GAMEPAD_B
FRONTENDACTION_RUP = XINPUT_GAMEPAD_RS_UP
FRONTENDACTION_RDOWN = XINPUT_GAMEPAD_RS_DOWN
FRONTENDACTION_RLEFT = XINPUT_GAMEPAD_RS_LEFT
FRONTENDACTION_RRIGHT = XINPUT_GAMEPAD_RS_RIGHT
FRONTENDACTION_BUTTON0 = XINPUT_GAMEPAD_RIGHT_SHOULDER
FRONTENDACTION_BUTTON1 = XINPUT_GAMEPAD_LEFT_SHOULDER
FRONTENDACTION_BUTTON2 = XINPUT_GAMEPAD_LEFT_THUMB
FRONTENDACTION_BUTTON3 = XINPUT_GAMEPAD_RIGHT_THUMB
FRONTENDACTION_BUTTON4 = XINPUT_GAMEPAD_X
FRONTENDACTION_BUTTON5 = XINPUT_GAMEPAD_Y
FRONTENDACTION_LTRIGGER = XINPUT_GAMEPAD_LT
FRONTENDACTION_RTRIGGER = XINPUT_GAMEPAD_RT
FRONTENDACTION_START = XINPUT_GAMEPAD_START
; In-Game controls
GAMEACTION_GAS = XINPUT_GAMEPAD_RT
GAMEACTION_BRAKE = XINPUT_GAMEPAD_LT
GAMEACTION_STEERLEFT = XINPUT_GAMEPAD_LS_LEFT
GAMEACTION_STEERRIGHT = XINPUT_GAMEPAD_LS_RIGHT
GAMEACTION_TURNLEFT = XINPUT_GAMEPAD_LS_LEFT
GAMEACTION_TURNRIGHT = XINPUT_GAMEPAD_LS_RIGHT
GAMEACTION_HANDBRAKE = XINPUT_GAMEPAD_A
GAMEACTION_NOS = XINPUT_GAMEPAD_B
GAMEACTION_GAMEBREAKER = XINPUT_GAMEPAD_X
GAMEACTION_SHIFTUP = XINPUT_GAMEPAD_RS_UP
GAMEACTION_SHIFTDOWN = XINPUT_GAMEPAD_RS_DOWN
GAMEACTION_RESET = XINPUT_GAMEPAD_BACK
HUDACTION_PAUSEREQUEST = XINPUT_GAMEPAD_START
HUDACTION_ENGAGE_EVENT = XINPUT_GAMEPAD_DPAD_UP
HUDACTION_PAD_LEFT = XINPUT_GAMEPAD_DPAD_LEFT
HUDACTION_PAD_DOWN = XINPUT_GAMEPAD_DPAD_DOWN
HUDACTION_PAD_RIGHT = XINPUT_GAMEPAD_DPAD_RIGHT
HUDACTION_SKIPNIS = XINPUT_GAMEPAD_A
HUDACTION_NEXTSONG = XINPUT_GAMEPAD_RIGHT_THUMB
CAMERAACTION_CHANGE = XINPUT_GAMEPAD_RIGHT_SHOULDER
CAMERAACTION_LOOKBACK = XINPUT_GAMEPAD_LEFT_SHOULDER
; Debug camera
CAMERAACTION_DEBUG = XINPUT_GAMEPAD_BACK
DEBUGACTION_DROPCAR = XINPUT_GAMEPAD_START
DEBUGACTION_MOVE_FORWARD = XINPUT_GAMEPAD_LS_UP
DEBUGACTION_MOVE_BACK = XINPUT_GAMEPAD_LS_DOWN
DEBUGACTION_MOVE_LEFT = XINPUT_GAMEPAD_LS_LEFT
DEBUGACTION_MOVE_RIGHT = XINPUT_GAMEPAD_LS_RIGHT
DEBUGACTION_MOVE_UP = XINPUT_GAMEPAD_RIGHT_SHOULDER
DEBUGACTION_MOVE_DOWN = XINPUT_GAMEPAD_LEFT_SHOULDER
DEBUGACTION_LOOK_UP = XINPUT_GAMEPAD_RS_DOWN
DEBUGACTION_LOOK_DOWN = XINPUT_GAMEPAD_RS_UP
DEBUGACTION_LOOK_LEFT = XINPUT_GAMEPAD_RS_LEFT
DEBUGACTION_LOOK_RIGHT = XINPUT_GAMEPAD_RS_RIGHT
DEBUGACTION_TURBO = XINPUT_GAMEPAD_LT
DEBUGACTION_SUPER_TURBO = XINPUT_GAMEPAD_RT
DEBUGACTION_LOOK_D_UP = XINPUT_GAMEPAD_DPAD_UP
DEBUGACTION_LOOK_D_DOWN = XINPUT_GAMEPAD_DPAD_DOWN
DEBUGACTION_LOOK_D_LEFT = XINPUT_GAMEPAD_DPAD_LEFT
DEBUGACTION_LOOK_D_RIGHT = XINPUT_GAMEPAD_DPAD_RIGHT
DEBUGACTION_MOVE_D_FORWARD = XINPUT_GAMEPAD_Y
DEBUGACTION_MOVE_D_BACK = XINPUT_GAMEPAD_A
DEBUGACTION_MOVE_D_LEFT = XINPUT_GAMEPAD_X
DEBUGACTION_MOVE_D_RIGHT = XINPUT_GAMEPAD_B
[EventsKB]
FRONTENDACTION_UP = VK_UP
FRONTENDACTION_DOWN = VK_DOWN
FRONTENDACTION_LEFT = VK_LEFT
FRONTENDACTION_RIGHT = VK_RIGHT
FRONTENDACTION_ACCEPT = VK_RETURN
FRONTENDACTION_CANCEL = VK_ESCAPE
FRONTENDACTION_RUP = VK_UP
FRONTENDACTION_RDOWN = VK_DOWN
FRONTENDACTION_RLEFT = VK_LEFT
FRONTENDACTION_RRIGHT = VK_RIGHT
FRONTENDACTION_BUTTON0 = 3
FRONTENDACTION_BUTTON1 = 5
FRONTENDACTION_BUTTON2 = T
FRONTENDACTION_BUTTON3 = R
FRONTENDACTION_BUTTON4 = 2
FRONTENDACTION_BUTTON5 = 1
FRONTENDACTION_LTRIGGER = 9
FRONTENDACTION_RTRIGGER = 0
FRONTENDACTION_START = 4
; In-Game controls
GAMEACTION_GAS = VK_UP
GAMEACTION_BRAKE = VK_DOWN
GAMEACTION_STEERLEFT = VK_LEFT
GAMEACTION_STEERRIGHT = VK_RIGHT
GAMEACTION_TURNLEFT = VK_LEFT
GAMEACTION_TURNRIGHT = VK_RIGHT
GAMEACTION_HANDBRAKE = VK_SPACE
GAMEACTION_NOS = X
GAMEACTION_GAMEBREAKER = G
GAMEACTION_SHIFTUP = VK_LSHIFT
GAMEACTION_SHIFTDOWN = VK_LCONTROL
GAMEACTION_RESET = R
HUDACTION_PAUSEREQUEST = VK_ESCAPE
HUDACTION_ENGAGE_EVENT = VK_RETURN
HUDACTION_PAD_LEFT = M
HUDACTION_PAD_RIGHT = VK_TAB
HUDACTION_PAD_DOWN = B
HUDACTION_SKIPNIS = VK_RETURN
CAMERAACTION_CHANGE = C
CAMERAACTION_LOOKBACK = L
CAMERAACTION_PULLBACK = P
; Debug camera
CAMERAACTION_DEBUG = VK_SUBTRACT
DEBUGACTION_DROPCAR = 5
DEBUGACTION_MOVE_FORWARD = W
DEBUGACTION_MOVE_BACK = S
DEBUGACTION_MOVE_LEFT = A
DEBUGACTION_MOVE_RIGHT = D
DEBUGACTION_MOVE_UP = VK_SPACE
DEBUGACTION_MOVE_DOWN = VK_LCONTROL
DEBUGACTION_LOOK_UP = K
DEBUGACTION_LOOK_DOWN = I
DEBUGACTION_LOOK_LEFT = J
DEBUGACTION_LOOK_RIGHT = L
DEBUGACTION_TURBO = VK_LSHIFT
DEBUGACTION_SUPER_TURBO = F
